using System.Data;
using System.Security.Claims;
using System.Text;
using ExcelDataReader;
using Huzur.Models;
using Huzur.Models.ViewModels;
using HuzurAksesuar.Entity.DTO;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MySqlConnector;

namespace Huzur.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class AdminController : ControllerBase
{
    private readonly HDbContext _context;
    private readonly ILogger<AdminController> _logger;

    public AdminController(HDbContext context, ILogger<AdminController> logger)
    {
        _context = context;
        _logger = logger;
    }

    [HttpPost("login")]
    [AllowAnonymous]
    public async Task<ActionResult<bool>> LoginAsync([FromBody] LoginRequestDTO request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        _logger.LogInformation("Login attempt for admin user");

        if (request.Password == "atural61")
        {
            var claims = new List<Claim>
            {
                new(ClaimTypes.Name, "huzur")
            };

            var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var principal = new ClaimsPrincipal(identity);

            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal);

            var cookieOptions = new CookieOptions
            {
                Expires = DateTimeOffset.Now.AddMonths(3),
                MaxAge = TimeSpan.FromDays(90),
                Secure = true,
                HttpOnly = true,
                SameSite = SameSiteMode.Lax
            };

            HttpContext.Response.Cookies.Append("loggedin", "true", cookieOptions);

            _logger.LogInformation("Admin login successful");
            return Ok(true);
        }

        _logger.LogWarning("Admin login failed - invalid password");
        return Unauthorized(false);
    }

    [HttpPost("logout")]
    public async Task<ActionResult> LogoutAsync()
    {
        await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
        HttpContext.Response.Cookies.Delete("loggedin");
        HttpContext.Response.Cookies.Delete("sessionid");

        _logger.LogInformation("Admin logout successful");
        return Ok(new { message = "Logout successful" });
    }

    [HttpGet("dashboard")]
    public async Task<ActionResult<AdminProductsModel>> GetDashboardAsync()
    {
        var model = new AdminProductsModel()
        {
            HProducts = await _context.HProducts.ToListAsync(),
            HProductInfos = await _context.HProductInfos.ToListAsync(),
            HCatalogs = await _context.HCatalogs.ToListAsync(),
            HSliders = await _context.HSliders.ToListAsync()
        };
        return Ok(model);
    }

    [HttpGet("products")]
    public async Task<ActionResult<AdminProductsModel>> GetProductsAsync()
    {
        var model = new AdminProductsModel()
        {
            HProducts = await _context.HProducts.ToListAsync(),
            HProductInfos = await _context.HProductInfos.ToListAsync(),
            HCategories = await _context.HCategories.ToListAsync(),
        };

        return Ok(model);
    }

    [HttpGet("product-list")]
    public async Task<ActionResult<AdminProductsModel>> GetProductListAsync()
    {
        var model = new AdminProductsModel()
        {
            HProducts = await _context.HProducts.ToListAsync(),
            HProductInfos = await _context.HProductInfos.ToListAsync()
        };
        return Ok(model);
    }

    [HttpGet("slider-management")]
    public async Task<ActionResult<AdminProductsModel>> GetSliderManagementAsync()
    {
        var model = new AdminProductsModel()
        {
            HCategories = await _context.HCategories.ToListAsync(),
            HSliders = await _context.HSliders.ToListAsync()
        };
        return Ok(model);
    }

    [HttpGet("catalogs")]
    public async Task<ActionResult<List<HCatalog>>> GetCatalogsAsync()
    {
        var catalogs = await _context.HCatalogs.ToListAsync();
        return Ok(catalogs);
    }

        [HttpPost("product")]
        public async Task<ActionResult<bool>> AddProductAsync(HProduct product, List<IFormFile> files, IFormFile _sketch)
        {
            try
            {
                if (product.CategoryId > 8)
                    product.mainct = HProduct.maincat.Kitchen;
                else if (product.CategoryId == 8)
                    product.mainct = HProduct.maincat.Bedroom;
                else if (product.CategoryId == 9)
                    product.mainct = HProduct.maincat.Bathroom;

                //product add
                var prdct = new HProduct();
                prdct = product;
                var _sketchname = await UploadSketchAsync(_sketch, prdct.id);
                product.sketch = _sketchname;
                product.date = DateTime.Now.ToString("dd/MM/yyyy");
                product.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");
                _context.HProducts.Add(product);
                await _context.SaveChangesAsync();
                // image add
                for (int i = 0; i < files.Count(); i++)
                {
                    var img = new HImage();
                    img.ProductId = prdct.id;
                    var _filename = await UploadImageAsync(files[i], prdct.id);
                    img.name = _filename;
                    _context.HImages.Add(img);
                    await _context.SaveChangesAsync();
                }
                //// info add
                //var info = productInfo;
                //productInfo.hcode = product.hcode;
                //productInfo.date = DateTime.Now.ToString("dd/MM/yyyy");
                //productInfo.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");
                //productInfo.ProductId = prdct.id;
                //context.HProductInfos.Add(info);
                //context.SaveChanges();

                return Ok(true);
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                ex.exinner = e.InnerException?.ToString();
                _context.Exceptions.Add(ex);
                await _context.SaveChangesAsync();
                return StatusCode(500, false);
            }

        }

        [HttpPut("product")]
        public async Task<ActionResult<bool>> UpdateProductAsync(HProduct product, List<IFormFile> files, IFormFile _sketch)
        {
            try
            {
                var _product = await _context.HProducts.FirstOrDefaultAsync(c => c.id == product.id);

                if (product.CategoryId < 8)
                    _product.mainct = HProduct.maincat.Kitchen;
                else if (product.CategoryId == 8)
                    _product.mainct = HProduct.maincat.Bedroom;
                else if (product.CategoryId == 9)
                    _product.mainct = HProduct.maincat.Bathroom;


                // skecht add
                if (_sketch != null)
                {
                    var _sketchname = await UploadSketchAsync(_sketch, _product.id);
                    _product.sketch = _sketchname;
                }

                // image add
                if (files.Count() != 0)
                {
                    for (int i = 0; i < files.Count(); i++)
                    {
                        var img = new HImage();
                        img.ProductId = _product.id;
                        var _filename = await UploadImageAsync(files[i], _product.id);
                        img.name = _filename;
                        _context.HImages.Add(img);
                        await _context.SaveChangesAsync();
                    }
                }


                //product update
                foreach (var prop in typeof(HProduct).GetProperties())
                {
                    var value = prop.GetValue(product);
                    if (value != null)
                    {
                        prop.SetValue(_product, value);
                    }
                }

                _product.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");
                _context.HProducts.Update(_product);
                await _context.SaveChangesAsync();

                return Ok(true);
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                _context.Exceptions.Add(ex);
                await _context.SaveChangesAsync();
                return StatusCode(500, false);
            }

        }

        [HttpGet("product-info/{id}")]
        public async Task<ActionResult<object>> GetProductInfoAsync(int id)
        {
            var prd = await _context.HProducts.FindAsync(id);
            if (prd == null)
            {
                return NotFound(new { message = "Product not found" });
            }

            var info = await _context.HProductInfos.Where(c => c.ProductId == prd.id).FirstOrDefaultAsync();
            if (info == null)
            {
                return NotFound(new { message = "Product info not found" });
            }

            var model = new List<object>();
            model.Add(prd.name);
            model.Add(prd.nameUS);
            model.Add(prd.price);
            model.Add(prd.priceUSD);
            model.Add(prd.description);
            model.Add(prd.descriptionUS);
            model.Add(prd.setuplink);
            model.Add(prd.promolink);
            model.Add(prd.sketch);
            model.Add(prd.Images);
            model.Add(prd.colors);
            model.Add(prd.CategoryId);

            return Ok(model);
        }

        [HttpGet("slider/{id}")]
        public async Task<ActionResult<HSlider>> GetSliderAsync(int id)
        {
            try
            {
                var slider = await _context.HSliders.FirstOrDefaultAsync(c => c.id == id);
                if (slider == null)
                {
                    return NotFound(new { message = "Slider not found" });
                }

                return Ok(slider);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting slider with ID: {SliderId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the slider" });
            }
        }

    [HttpPost("slider")]
    public async Task<ActionResult<HSlider>> AddSliderAsync([FromForm] AddSliderRequestDTO request, IFormFile? file)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var slider = new HSlider
            {
                upperText = request.UpperText,
                lowerText = request.LowerText,
                midText = request.MidText,
                CategoryId = request.CategoryId,
                date = DateTime.Now.ToString("dd/MM/yyyy"),
                updatedDate = DateTime.Now.ToString("dd/MM/yyyy"),
                isSliderActive = false
            };

            if (file != null)
            {
                var ext = new FileInfo(file.FileName).Extension;
                var uploadsFolder = Path.Combine("wwwroot/images");
                var uniqueFileName = "huzurSlider" + Guid.NewGuid().ToString()[..10] + ext;
                var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                await using var fileStream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(fileStream);

                slider.sliderImage = uniqueFileName;
            }

            _context.HSliders.Add(slider);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Slider added successfully with ID: {SliderId}", slider.id);
            return Ok(slider);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding slider");
            return StatusCode(500, new { message = "An error occurred while adding the slider" });
        }
    }

    [HttpPut("slider")]
    public async Task<ActionResult<string>> UpdateSliderAsync([FromForm] UpdateSliderRequestDTO request, IFormFile? file)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        try
        {
            var slider = await _context.HSliders.FindAsync(request.Id);
            if (slider == null)
            {
                return NotFound(new { message = "Slider not found" });
            }

            slider.upperText = request.UpperText;
            slider.lowerText = request.LowerText;
            slider.midText = request.MidText;
            slider.CategoryId = request.CategoryId;
            slider.updatedDate = DateTime.Now.ToString("dd/MM/yyyy");

            string uniqueFileName = "";
            if (file != null)
            {
                var ext = new FileInfo(file.FileName).Extension;
                var uploadsFolder = Path.Combine("wwwroot/images");
                uniqueFileName = "huzurSlider" + Guid.NewGuid().ToString()[..10] + ext;
                var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                await using var fileStream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(fileStream);

                slider.sliderImage = uniqueFileName;
            }

            _context.HSliders.Update(slider);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Slider updated successfully with ID: {SliderId}", request.Id);
            return Ok(uniqueFileName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating slider with ID: {SliderId}", request.Id);
            return StatusCode(500, new { message = "An error occurred while updating the slider" });
        }
    }

    [HttpPatch("slider/{id}/active")]
    public async Task<ActionResult<bool>> SetSliderActiveAsync(int id, [FromBody] bool isActive)
    {
        try
        {
            var slider = await _context.HSliders.FirstOrDefaultAsync(c => c.id == id);
            if (slider == null)
            {
                return NotFound(new { message = "Slider not found" });
            }

            slider.isSliderActive = isActive;
            _context.HSliders.Update(slider);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Slider {SliderId} active status set to {IsActive}", id, isActive);
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting slider {SliderId} active status", id);
            return StatusCode(500, new { message = "An error occurred while updating the slider" });
        }
    }

    [HttpDelete("slider/{id}")]
    public async Task<ActionResult<bool>> RemoveSliderAsync(int id)
    {
        try
        {
            var slider = await _context.HSliders.FirstOrDefaultAsync(c => c.id == id);
            if (slider == null)
            {
                return NotFound(new { message = "Slider not found" });
            }

            _context.HSliders.Remove(slider);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Slider {SliderId} removed successfully", id);
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing slider {SliderId}", id);
            return StatusCode(500, new { message = "An error occurred while removing the slider" });
        }
    }

    [HttpDelete("product/{id}")]
    public async Task<ActionResult<bool>> DeleteProductAsync(int id)
    {
        try
        {
            var product = await _context.HProducts.FindAsync(id);
            if (product == null)
            {
                return NotFound(new { message = "Product not found" });
            }

            _context.HProducts.Remove(product);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Product deleted successfully with ID: {ProductId}", id);
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting product with ID: {ProductId}", id);
            return StatusCode(500, new { message = "An error occurred while deleting the product" });
        }
    }


        private async Task<string> UploadImageAsync(IFormFile _file, int productId)
        {
            try
            {
                string uniqueFileName = "";
                var ext = new FileInfo(_file.FileName).Extension;

                string uploadsFolder = Path.Combine("wwwroot/images");
                uniqueFileName = "huzur" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    _file.CopyTo(fileStream);
                }

                return uniqueFileName;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                _context.Exceptions.Add(ex);
                await _context.SaveChangesAsync();
                return "error";
            }

        }

        private async Task<string> UploadSketchAsync(IFormFile _file, int productId)
        {
            try
            {
                string uniqueFileName = "";
                var ext = new FileInfo(_file.FileName).Extension;

                string uploadsFolder = Path.Combine("wwwroot/sketches");
                uniqueFileName = "huzur" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    _file.CopyTo(fileStream);
                }

                return uniqueFileName;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                ex.exinner = e.InnerException?.ToString();
                _context.Exceptions.Add(ex);
                await _context.SaveChangesAsync();
                return "error";
            }

        }

        [HttpPost("catalog")]
        public async Task<ActionResult<object>> AddCatalogAsync([FromForm] string filename, IFormFile file)
        {
            try
            {
                var ext = new FileInfo(file.FileName).Extension;
                var uploadsFolder = Path.Combine("wwwroot/catalogs");
                var uniqueFileName = "huzur" + Guid.NewGuid().ToString()[..10] + ext;
                var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                await using var fileStream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(fileStream);

                var catalog = new HCatalog()
                {
                    name = filename,
                    date = DateTime.Now.ToString("dd/MM/yyyy"),
                    link = uniqueFileName,
                    size = file.Length.ToString()
                };

                _context.HCatalogs.Add(catalog);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Catalog added successfully: {CatalogName}", filename);
                return Ok(catalog);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding catalog: {CatalogName}", filename);
                return StatusCode(500, new { message = "An error occurred while adding the catalog" });
            }

        }

    [HttpDelete("catalog/{id}")]
    public async Task<ActionResult<bool>> DeleteCatalogAsync(int id)
    {
        try
        {
            var catalog = await _context.HCatalogs.FindAsync(id);
            if (catalog == null)
            {
                return NotFound(new { message = "Catalog not found" });
            }

            _context.HCatalogs.Remove(catalog);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Catalog deleted successfully with ID: {CatalogId}", id);
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting catalog with ID: {CatalogId}", id);
            return StatusCode(500, new { message = "An error occurred while deleting the catalog" });
        }
    }

        public async Task ReadExcelFileAsync(bool ok, string filePath, IFormFile _file)
        {
            try
            {
                filePath = await UploadExcelAsync(_file);
                if (ok)
                {
                    using (var stream = System.IO.File.Open(filePath, FileMode.Open, FileAccess.Read))
                    {
                        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                        using (var reader = ExcelReaderFactory.CreateOpenXmlReader(stream))
                        {
                            var result = reader.AsDataSet(new ExcelDataSetConfiguration()
                            {
                                ConfigureDataTable = (data) => new ExcelDataTableConfiguration()
                                {
                                    UseHeaderRow = true
                                }
                            });
                            await ConvertDataTableToMySqlAsync(result.Tables[0], "HProductInfos");
                        }
                    }

                    var xls = new HExcelF()
                    {
                        name = filePath,
                        date = DateTime.Now.ToString("dd/MM/yyyy")
                    };
                    _context.HExcelF.Add(xls);
                    await _context.SaveChangesAsync();

                }
                else
                {
                    using (var stream = System.IO.File.Open(filePath, FileMode.Open, FileAccess.Read))
                    {
                        Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);
                        using (var reader = ExcelReaderFactory.CreateOpenXmlReader(stream))
                        {
                            var result = reader.AsDataSet(new ExcelDataSetConfiguration()
                            {
                                ConfigureDataTable = (data) => new ExcelDataTableConfiguration()
                                {
                                    UseHeaderRow = true
                                }
                            });
                            await ConvertDataTableToMySqlAsync(result.Tables[0], "HProducts");
                        }
                    }
                }
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                _context.Exceptions.Add(ex);
                await _context.SaveChangesAsync();
            }
        }

        private async Task<string> UploadExcelAsync(IFormFile _file)
        {
            try
            {
                string uniqueFileName = "";
                var ext = new FileInfo(_file.FileName).Extension;

                string uploadsFolder = Path.Combine("wwwroot/exceltables");
                uniqueFileName = "huzur" + Guid.NewGuid().ToString().Substring(0, 10) + ext;
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    _file.CopyTo(fileStream);
                }

                return "wwwroot/exceltables/" + uniqueFileName;
            }
            catch (Exception e)
            {
                var ex = new Exceptions();
                ex.exmessage = e.Message.ToString();
                ex.exinner = e.InnerException?.ToString();
                _context.Exceptions.Add(ex);
                await _context.SaveChangesAsync();
                return "error";
            }

        }

        public async Task ConvertDataTableToMySqlAsync(DataTable table, string tableName)
        {
            var _id = 171;
            var con = new MySqlConnection("Server=n1nlmysql17plsk.secureserver.net;Port=3306;Database=huzur;Uid=remzi;Pwd=*******;AllowUserVariables=True;");
            con.Open();
            var sql = new StringBuilder();
            if (tableName == "HProductInfos")
            {
                foreach (DataRow row in table.Rows)
                {
                    //var columns = new List<string>();
                    var columnsNames = "";
                    //var parameters = new List<MySqlParameter>();
                    var parameterNames = new List<string>();
                    foreach (DataColumn col in table.Columns)
                    {
                        var parameter = new MySqlParameter($"@{col.ColumnName}", row[col.ColumnName]);
                        parameterNames.Add(parameter.Value.ToString());
                    }
                    var command = new MySqlCommand($"INSERT INTO `HProductInfos` (`id`, `hcode`, `wdh`, `basket`, `c`, `volume`, `weight`, `date`, `updatedDate`, `ProductId`) VALUES (NULL,'{parameterNames[0]}','{parameterNames[1]}','{parameterNames[2]}','{parameterNames[3]}','{parameterNames[4]}','{parameterNames[5]}','{parameterNames[6].Substring(0, 10)}','{parameterNames[7].Substring(0, 10)}','{parameterNames[8]}' );", con);
                    command.ExecuteNonQuery();
                    await _context.SaveChangesAsync();
                }
            }
            else
            {
                foreach (DataRow row in table.Rows)
                {
                    var image = "";
                    var image2 = "";
                    var image3 = "";

                    var parameterNames = new List<string>();
                    foreach (DataColumn col in table.Columns)
                    {
                        var parameter = new MySqlParameter($"@{col.ColumnName}", row[col.ColumnName]);
                        parameterNames.Add(parameter.Value.ToString());

                        if (col.ColumnName == "image")
                            image = parameter.Value.ToString();
                        if (col.ColumnName == "image2")
                            image2 = parameter.Value.ToString();
                        if (col.ColumnName == "image3")
                            image3 = parameter.Value.ToString();

                    }


                    var command = new MySqlCommand($"INSERT INTO `HProducts` (`id`, `name`, `nameUS`, `nameAR`,`price`,`priceUSD`,`priceEUR`,`mainct`,`date`,`updatedDate`,`CategoryId`,`colors`) VALUES (NULL,'{parameterNames[0]}','{parameterNames[1]}','{parameterNames[2]}','{parameterNames[3]}','{parameterNames[4]}','{parameterNames[5]}','{parameterNames[6]}','{parameterNames[7].Substring(0, 10)}','{parameterNames[8].Substring(0, 10)}','{parameterNames[9]}','{parameterNames[10]}');", con);
                    command.ExecuteNonQuery();
                    await _context.SaveChangesAsync();

                    var img = new HImage()
                    {
                        ProductId = _id,
                        name = image
                    };
                    var img2 = new HImage()
                    {
                        ProductId = _id,
                        name = image2
                    };
                    var img3 = new HImage()
                    {
                        ProductId = _id,
                        name = image3
                    };

                    _context.HImages.Add(img);
                    _context.HImages.Add(img2);
                    _context.HImages.Add(img3);
                    await _context.SaveChangesAsync();
                    _id++;
                }
            }

            con.Close();
        }

    [HttpDelete("excel/{id}")]
    public async Task<ActionResult<bool>> DeleteExcelAsync(int id)
    {
        try
        {
            var excel = await _context.HExcelF.FindAsync(id);
            if (excel == null)
            {
                return NotFound(new { message = "Excel file not found" });
            }

            _context.HExcelF.Remove(excel);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Excel file deleted successfully with ID: {ExcelId}", id);
            return Ok(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting excel file with ID: {ExcelId}", id);
            return StatusCode(500, new { message = "An error occurred while deleting the excel file" });
        }
    }
}

