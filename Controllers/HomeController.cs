﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Huzur.Models;
using Huzur.Models.ViewModels;
using static Huzur.Models.HProduct;
using Microsoft.EntityFrameworkCore;
using System.Net;   
using System.Net.Mail;
using System.Net.Http;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using System.Globalization;
using System.Threading;
using HuzurAksesuar.Entity.DTO;

namespace Huzur.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class HomeController : Controller
    {
        private ProductRepository _productRepository;

        public HomeController(ProductRepository _productRepository)
        {
            this._productRepository = _productRepository;
        }

        [HttpGet("getSliders")]
        public async Task<ActionResult<List<SliderDTO>>> GetSliders()
        {
            var sliders = await _productRepository.GetAllSliders(); // Slider verilerini al
            return Ok(sliders); // JSON olarak döndür
        }


        [HttpGet("getPopular/{mainct}")]
        public async Task<ActionResult<List<ProductDTO>>> GetPopularByCategory(HProduct.maincat mainct)
        {
            if (mainct == HProduct.maincat.All)
                return Ok(await _productRepository.FindByPopularTrue());
            else
                return Ok(await _productRepository.GetPopularByMainCategory(mainct));
        }


        [HttpGet("GetVideos/{cat}/{row}")]
        public async Task<ActionResult<VideoPageModel>> GetVideos(int? cat, int? row)
        {
            cat = cat ?? 0;
            row = row ?? 1;

            List<HProduct> filters = new List<HProduct>();

            if (cat != 0)
            {
                var productsByCategory = await _productRepository.GetVideosByCategoryId(cat.Value); 
                filters.AddRange(productsByCategory); 
            }
            else
            {
                var videos = await _productRepository.GetVideos(); 
                filters.AddRange(videos); 
            }

            filters = filters.Distinct().ToList();

            double p = (double)filters.Count / 10;
            int rowCount = (int)Math.Ceiling(p);

            filters = filters.OrderByDescending(x => x.id).ToList(); 

            filters = filters.Skip((row.Value - 1) * 10).Take(10).ToList();

            var model = new VideoPageModel
            {
                hProductList = filters,
                rowCount = rowCount
            };

            return Ok(model);
        }


        [HttpGet("GetProducts/{cat}/{row}/{sort}")]
        public async Task<ActionResult<VideoPageModel>> GetProducts(int? cat, int? row, int? sort)
        {
            cat = cat ?? 0;
            row = row ?? 1;

            List<HProduct> filters = new List<HProduct>();

            if (cat != 0)
            {
                var productsByCategory = await _productRepository.GetProductsByCategoryId(cat.Value); // Asenkron bekleme
                filters.AddRange(productsByCategory); // Listeyi ekleme
            }
            else
            {
                var products = await _productRepository.GetProducts(); // Asenkron bekleme
                filters.AddRange(products); // Listeyi ekleme
            }

            filters = filters.Distinct().ToList();

            // Apply sorting based on 'sort' value
            switch (sort ?? 0)
            {
                case 0:
                    filters = filters.OrderByDescending(x => x.saleRate).ToList();
                    break;
                case 1:
                    filters = filters.OrderBy(x => x.saleRate).ToList();
                    break;
                case 2:
                    filters = filters.OrderByDescending(x => x.date).ToList();
                    break;
                case 3:
                    filters = filters.OrderBy(x => x.date).ToList();
                    break;
            }

            double p = (double)filters.Count / 20;
            int rowCount = (int)Math.Ceiling(p);

            filters = filters.Skip((row.Value - 1) * 20).Take(20).ToList();

            var model = new VideoPageModel
            {
                hProductList = filters,
                rowCount = rowCount,
                AllProductCount = await _productRepository.GetProductsCountAsync()
            };

            return Ok(model);
        }


        [HttpGet("getAllCategories")]
        public async Task<ActionResult<List<CategoryDTO>>> GetAllCategories()
        {
            var categories = await _productRepository.GetAllCategories();
            return Ok(categories);
        }


        [HttpGet("getCategoryById")]
        public async Task<ActionResult<HCategory>> GetCategoryByIdAsync([FromQuery] int? id)
        {
            if (!id.HasValue)
            {
                return BadRequest("ID is required");
            }

            var category = await _productRepository.FindById(id.Value);
            if (category == null)
            {
                return NotFound("Category not found");
            }

            return Ok(category);
        }

        [HttpGet("getProductById/{id}")]
        public async Task<ActionResult<DetailsDTO>> GetProductById(int id)
        {
            var prd = await _productRepository.GetProductById(id);

            return Ok(prd);
        }

        [HttpGet("getProductsByCtId/{id}")]
        public async Task<ActionResult<ProductDTO>> GetProductByCtId(int id)
        {
            var prd = await _productRepository.GetProductByCtId(id);

            return Ok(prd);
        }
    }
}

