﻿using Huzur.Models;
using HuzurAksesuar.Entity.DTO;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

public class ProductRepository
{
    private readonly HDbContext _context;

    public ProductRepository(HDbContext context)
    {
        _context = context;
    }

    // Videoları Döndüren
    public async Task<List<HProduct>> GetVideos()
    {
        return await _context.HProducts
            .Where(h => h.promolink != null)
            .Select(h => new HProduct
            {
                promolink = h.promolink
            })
            .ToListAsync();
    }

    // Kategori ID'ye Göre Videoları Döndüren
    public async Task<List<HProduct>> GetVideosByCategoryId(int categoryId)
    {
        return await _context.HProducts
            .Where(h => h.promolink != null && h.Category.id == categoryId)
            .ToListAsync();
    }

    // İsimle Arama Yapan
    public async Task<List<HProduct>> SearchByName(string keyword)
    {
        return await _context.HProducts
            .Where(h => h.name.Contains(keyword))
            .ToListAsync();
    }

    // Popüler Ürünleri Döndüren
    public async Task<List<HProduct>> FindByPopularTrue()
    {
        return await _context.HProducts
            .Where(h => h.popular)
            .Include(h => h.Images)
            .ToListAsync();
    }

    // Ana Kategoriye Göre Popüler Ürünleri Döndüren
    public async Task<List<HProduct>> GetPopularByMainCategory(HProduct.maincat mainct)
    {
        return await _context.HProducts
          .Where(p => p.popular && p.mainct == mainct)
          .Select(p => new HProduct
          {
              id = p.id,
              name = p.name,
              nameUS = p.nameUS,
              nameAR = p.nameAR,
              colors = p.colors,
              Images = p.Images.Select(i => new HImage
              {
                  id = i.id,
                  name = i.name
              }).ToList()
          })
          .ToListAsync();
    }

    public async Task<int> GetProductsCountAsync()
    {
        return await _context.HProducts.CountAsync();
    }

    // Tüm Ürünleri Döndüren
    public async Task<List<HProduct>> GetProducts()
    {
        return await _context.HProducts
            .Select(p => new HProduct
            {
                id = p.id,
                name = p.name,
                nameUS = p.nameUS,
                nameAR = p.nameAR,
                colors = p.colors,
                Images = p.Images.Select(i => new HImage
                {
                    id = i.id,
                    name = i.name
                }).ToList()
            })
            .ToListAsync();
    }

    // Kategori ID'ye Göre Ürünleri Döndüren
    public async Task<List<HProduct>> GetProductsByCategoryId(int categoryId)
    {
        return await _context.HProducts
            .Where(h => h.Category.id == categoryId)
            .Select(p => new HProduct
            {
                id = p.id,
                name = p.name,
                nameUS = p.nameUS,
                nameAR = p.nameAR,
                colors = p.colors,
                Images = p.Images.Select(i => new HImage
                {
                    id = i.id,
                    name = i.name
                }).ToList()
            })
            .ToListAsync();
    }

    public async Task<List<HSlider>> GetAllSliders()
    {
        return await _context.HSliders
            .Select(p => new HSlider
            {
                id = p.id,
                upperText = p.upperText,
                upperTextEn = p.upperTextEn,
                upperTextAr = p.upperTextAr,
                lowerText = p.lowerText,
                lowerTextAr = p.lowerTextAr,
                lowerTextEn = p.lowerTextEn,
                midText = p.midText,
                midTextAr = p.midTextAr,
                midTextEn = p.midTextEn,
                CategoryId = p.CategoryId,
                sliderImage = p.sliderImage
            })
            .ToListAsync();
    }

    public async Task<HCategory> GetCategoryById(int id)
    {
        return await _context.HCategories
            .FirstOrDefaultAsync(h => h.id == id);
    }

    public async Task<List<CategoryDTO>> GetAllCategories()
    {
        return await _context.HCategories
            .Select(c => new CategoryDTO
            {
                Id = c.id,
                Name = c.name,
                ProductCount = c.Products.Count()
            })
            .ToListAsync();
    }

    public async Task<HCategory> FindById(int id)
    {
        return await _context.HCategories
            .FirstOrDefaultAsync(h => h.id == id);
    }

    public async Task<HProduct> GetProductById(int id)
    {
        return await _context.HProducts
            .Where(p => p.id == id)
            .Select(p => new HProduct
            {
                id = p.id,
                name = p.name,
                nameUS = p.nameUS,
                nameAR = p.nameAR,
                colors = p.colors,
                Images = p.Images.Select(i => new HImage
                {
                    id = i.id,
                    name = i.name
                }).ToList(),
                ProductInfos = p.ProductInfos,
                CategoryId = p.CategoryId
            })
            .FirstOrDefaultAsync();
    }

    public async Task<List<HProduct>> GetProductByCtId(int id)
    {
        return await _context.HProducts
            .Where(p => p.CategoryId == id)
            .Select(p => new HProduct
            {
                id = p.id,
                name = p.name,
                nameUS = p.nameUS,
                nameAR = p.nameAR,
                colors = p.colors,
                Images = p.Images.Select(i => new HImage
                {
                    id = i.id,
                    name = i.name
                }).ToList()
            })
            .Take(5)
            .ToListAsync();
    }
}
