using System.ComponentModel.DataAnnotations;

namespace HuzurAksesuar.Entity.DTO;

public class UpdateSliderRequestDTO
{
    [Required(ErrorMessage = "ID is required")]
    [Range(1, int.MaxValue, ErrorMessage = "ID must be a positive number")]
    public int Id { get; set; }

    [Required(ErrorMessage = "Lower text is required")]
    [StringLength(200, ErrorMessage = "Lower text must be between 1 and 200 characters", MinimumLength = 1)]
    public string LowerText { get; set; } = string.Empty;

    [Required(ErrorMessage = "Upper text is required")]
    [StringLength(200, ErrorMessage = "Upper text must be between 1 and 200 characters", MinimumLength = 1)]
    public string UpperText { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "Mid text must not exceed 200 characters")]
    public string? MidText { get; set; }

    [Required(ErrorMessage = "Category ID is required")]
    [Range(1, int.MaxValue, ErrorMessage = "Category ID must be a positive number")]
    public int CategoryId { get; set; }
}
