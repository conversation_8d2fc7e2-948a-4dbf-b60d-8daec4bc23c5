﻿using System.ComponentModel.DataAnnotations;

namespace HuzurAksesuar.Entity.DTO;

public class ProductDTO
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Product name is required")]
    [StringLength(200, ErrorMessage = "Product name must not exceed 200 characters")]
    public string Name { get; set; } = string.Empty;

    [StringLength(200, ErrorMessage = "US name must not exceed 200 characters")]
    public string? NameUS { get; set; }

    [StringLength(200, ErrorMessage = "Arabic name must not exceed 200 characters")]
    public string? NameAR { get; set; }

    [StringLength(100, ErrorMessage = "Colors must not exceed 100 characters")]
    public string? Colors { get; set; }

    public ICollection<ImageDTO> Images { get; set; } = new List<ImageDTO>();
}

