﻿using System.ComponentModel.DataAnnotations;

namespace HuzurAksesuar.Entity.DTO;

public class CategoryDTO
{
    public int Id { get; set; }

    [Required(ErrorMessage = "Category name is required")]
    [StringLength(100, ErrorMessage = "Category name must not exceed 100 characters")]
    public string Name { get; set; } = string.Empty;

    [Range(0, int.MaxValue, ErrorMessage = "Product count must be non-negative")]
    public int ProductCount { get; set; }

    public IEnumerable<ProductDTO> Products { get; set; } = new List<ProductDTO>();
}

