using System;
using Huzur.Models;
using System.Collections.Generic;
using System.IO.Compression;
using System.Reflection.Emit;
using System.Xml.Linq;
using Microsoft.EntityFrameworkCore;

namespace Huzur.Models
{
    public class HDbContext : DbContext
    {
        public HDbContext(DbContextOptions<HDbContext> options) : base(options)
        {
        }

        public DbSet<HProduct> HProducts { get; set; }
        public DbSet<HCategory> HCategories { get; set; }
        public DbSet<HProductInfo> HProductInfos { get; set; }
        public DbSet<HImage> HImages { get; set; }
        public DbSet<HExcelF> HExcelF { get; set; }
        public DbSet<HCatalog> HCatalogs { get; set; }
        public DbSet<HSlider> HSliders { get; set; }
        public DbSet<Exceptions> Exceptions { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<HProduct>()
                .HasMany(p => p.Images)
                .WithOne()
                .HasForeignKey(img => img.ProductId);

            modelBuilder.Entity<HProduct>()
                .HasMany(p => p.ProductInfos)
                .WithOne()
                .HasForeignKey(info => info.ProductId);
        }

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                var connectionString = "Host=*************;Database=huzurdb;Username=remzi;Password=**************";
                optionsBuilder.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString));
            }
        }
    }
}


