﻿<Properties StartupConfiguration="{79F4FDA9-0592-4968-B21E-D0AECBDF032D}|Default">
  <MultiItemStartupConfigurations />
  <MonoDevelop.Ide.ItemProperties.HuzurAksesuar PreferredExecutionTarget="/Applications/Google Chrome.app" />
  <MonoDevelop.Ide.Workbench ActiveDocument="Controllers/HomeController.cs">
    <Files>
      <File FileName="Entity/HCatalog.cs" />
      <File FileName="Entity/HProductInfo.cs" />
      <File FileName="Entity/HImage.cs" />
      <File FileName="Controllers/HomeController.cs" Line="129" Column="29" />
      <File FileName="Repository/CategoryRepository.cs" />
      <File FileName="Repository/ProductRepository.cs" />
    </Files>
    <Pads>
      <Pad Id="ProjectPad">
        <State name="__root__">
          <Node name="<PERSON><PERSON>rAksesuar">
            <Node name="<PERSON><PERSON>r<PERSON>ksesuar">
              <Node name="ConnectedServicesFolder" selected="True" />
              <Node name="Controllers" expanded="True" />
              <Node name="Entity" expanded="True">
                <Node name="DTO" expanded="True" />
              </Node>
              <Node name="Repository" expanded="True" />
            </Node>
          </Node>
        </State>
      </Pad>
    </Pads>
  </MonoDevelop.Ide.Workbench>
  <MonoDevelop.Ide.DebuggingService.Breakpoints>
    <BreakpointStore />
  </MonoDevelop.Ide.DebuggingService.Breakpoints>
  <MonoDevelop.Ide.Workspace ActiveConfiguration="Debug" />
  <MonoDevelop.Ide.DebuggingService.PinnedWatches />
</Properties>