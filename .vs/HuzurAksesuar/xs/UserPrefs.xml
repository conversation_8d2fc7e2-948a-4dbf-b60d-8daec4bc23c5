﻿<Properties StartupConfiguration="{79F4FDA9-0592-4968-B21E-D0AECBDF032D}|Default">
  <MultiItemStartupConfigurations />
  <MonoDevelop.Ide.Workbench ActiveDocument="Controllers/HomeController.cs">
    <Files>
      <File FileName="Entity/HCatalog.cs" />
      <File FileName="Entity/HProductInfo.cs" />
      <File FileName="Entity/HImage.cs" />
      <File FileName="Controllers/HomeController.cs" Line="1" Column="1" />
      <File FileName="Repository/CategoryRepository.cs" />
      <File FileName="Repository/ProductRepository.cs" />
    </Files>
    <Pads>
      <Pad Id="ProjectPad">
        <State name="__root__">
          <Node name="HuzurAksesuar" expanded="True">
            <Node name="<PERSON>zurAksesuar" expanded="True">
              <Node name="Controllers" expanded="True">
                <Node name="HomeController.cs" selected="True" />
              </Node>
              <Node name="Entity" expanded="True">
                <Node name="DTO" expanded="True" />
              </Node>
              <Node name="Repository" expanded="True" />
            </Node>
          </Node>
        </State>
      </Pad>
    </Pads>
  </MonoDevelop.Ide.Workbench>
  <MonoDevelop.Ide.ItemProperties.HuzurAksesuar PreferredExecutionTarget="/Applications/Google Chrome.app" />
  <MonoDevelop.Ide.DebuggingService.PinnedWatches />
  <MonoDevelop.Ide.DebuggingService.Breakpoints>
    <BreakpointStore />
  </MonoDevelop.Ide.DebuggingService.Breakpoints>
  <MonoDevelop.Ide.Workspace ActiveConfiguration="Debug" />
</Properties>