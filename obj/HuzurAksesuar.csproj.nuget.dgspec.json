{"format": 1, "restore": {"/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj": {}}, "projects": {"/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj", "projectName": "Hu<PERSON>r<PERSON>ks<PERSON>ua<PERSON>", "projectPath": "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/HuzurAksesuar.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/Pixedi Digital Agency/HuzurAksesuar/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ExcelDataReader": {"target": "Package", "version": "[3.7.0, )"}, "ExcelDataReader.DataSet": {"target": "Package", "version": "[3.7.0, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.8, )"}, "MySqlConnector": {"target": "Package", "version": "[2.3.7, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.8, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[8.0.2, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.8.1, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Host.osx-arm64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.303/PortableRuntimeIdentifierGraph.json"}}}}}